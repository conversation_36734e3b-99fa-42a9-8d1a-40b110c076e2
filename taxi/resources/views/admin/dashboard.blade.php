@push('plugin-styles')

@endpush

@push('plugin-scripts')

@endpush


@extends('layouts.app')



@section('content')
<div class="container-fluid dashboard-stats">
    <div class="row g-4 mb-4">
        <div class="col-12 col-sm-6 col-lg-3">
            <div class="stat-card stat-users">
                <div class="stat-accent"></div>
                <div class="d-flex align-items-center">
                    <div class="stat-icon text-primary"><i class="bi bi-people"></i></div>
                    <div class="ms-3">
                        <div class="stat-label">Total Users</div>
                        <div class="stat-value">{{ number_format($totalUsers) }}</div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-12 col-sm-6 col-lg-3">
            <div class="stat-card stat-drivers">
                <div class="stat-accent"></div>
                <div class="d-flex align-items-center">
                    <div class="stat-icon text-success"><i class="bi bi-person-badge"></i></div>
                    <div class="ms-3">
                        <div class="stat-label">Total Drivers</div>
                        <div class="stat-value">{{ number_format($totalDrivers) }}</div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-12 col-sm-6 col-lg-3">
            <div class="stat-card stat-trips">
                <div class="stat-accent"></div>
                <div class="d-flex align-items-center">
                    <div class="stat-icon text-warning"><i class="bi bi-car-front"></i></div>
                    <div class="ms-3">
                        <div class="stat-label">Total Trips</div>
                        <div class="stat-value">{{ number_format($totalTrips) }}</div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-12 col-sm-6 col-lg-3">
            <div class="stat-card stat-earnings">
                <div class="stat-accent"></div>
                <div class="d-flex align-items-center">
                    <div class="stat-icon text-info"><i class="bi bi-currency-dollar"></i></div>
                    <div class="ms-3">
                        <div class="stat-label">Total Earnings</div>
                        <div class="stat-value">${{ number_format($totalEarnings, 2) }}</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="row g-4 mb-4">
        <div class="col-12 col-sm-4">
            <div class="stat-card stat-completed">
                <div class="stat-accent"></div>
                <div class="d-flex align-items-center">
                    <div class="stat-icon text-success"><i class="bi bi-check-circle"></i></div>
                    <div class="ms-3">
                        <div class="stat-label">Completed Trips</div>
                        <div class="stat-value">{{ number_format($completedTrips) }}</div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-12 col-sm-4">
            <div class="stat-card stat-cancelled">
                <div class="stat-accent"></div>
                <div class="d-flex align-items-center">
                    <div class="stat-icon text-danger"><i class="bi bi-x-circle"></i></div>
                    <div class="ms-3">
                        <div class="stat-label">Cancelled Trips</div>
                        <div class="stat-value">{{ number_format($cancelledTrips) }}</div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-12 col-sm-4">
            <div class="stat-card stat-ongoing">
                <div class="stat-accent"></div>
                <div class="d-flex align-items-center">
                    <div class="stat-icon text-warning"><i class="bi bi-clock-history"></i></div>
                    <div class="ms-3">
                        <div class="stat-label">Ongoing Trips</div>
                        <div class="stat-value">{{ number_format($ongoingTrips) }}</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row g-4 mb-4">
        <div class="col-12">
            <div class="card shadow-lg border-0 chart-card">
                <div class="card-body d-flex flex-column align-items-center justify-content-center">
                    <h5 class="card-title mb-4">Trips Distribution</h5>
                    <canvas id="tripsChart" height="140" style="max-width: 400px;"></canvas>
                </div>
            </div>
        </div>
    </div>
</div>
@push('plugin-styles')
    <link href="{{ asset('assets/css/bootstrap-icons.css') }}" rel="stylesheet">
    <style>
        .dashboard-stats .stat-card {
            border-radius: 1.2rem;
            box-shadow: 0 2px 16px 0 rgba(60,72,100,.08), 0 1.5px 4px 0 rgba(60,72,100,.04);
            background: #fff;
            padding: 1.5rem 1.2rem;
            position: relative;
            transition: transform 0.15s, box-shadow 0.15s;
            min-height: 110px;
            overflow: hidden;
        }
        .dashboard-stats .stat-card:hover {
            transform: translateY(-4px) scale(1.025);
            box-shadow: 0 6px 32px 0 rgba(60,72,100,.16), 0 2px 8px 0 rgba(60,72,100,.08);
        }
        .dashboard-stats .stat-accent {
            height: 5px;
            width: 100%;
            position: absolute;
            top: 0; left: 0;
            border-radius: 1.2rem 1.2rem 0 0;
            background: linear-gradient(90deg, #4e73df 0%, #1cc88a 100%);
            opacity: 0.18;
        }
        .dashboard-stats .stat-users .stat-accent { background: linear-gradient(90deg, #4e73df 0%, #36b9cc 100%); }
        .dashboard-stats .stat-drivers .stat-accent { background: linear-gradient(90deg, #1cc88a 0%, #198754 100%); }
        .dashboard-stats .stat-trips .stat-accent { background: linear-gradient(90deg, #f6c23e 0%, #e0a800 100%); }
        .dashboard-stats .stat-earnings .stat-accent { background: linear-gradient(90deg, #36b9cc 0%, #0dcaf0 100%); }
        .dashboard-stats .stat-completed .stat-accent { background: linear-gradient(90deg, #198754 0%, #1cc88a 100%); }
        .dashboard-stats .stat-cancelled .stat-accent { background: linear-gradient(90deg, #dc3545 0%, #f6c23e 100%); }
        .dashboard-stats .stat-ongoing .stat-accent { background: linear-gradient(90deg, #ffc107 0%, #fd7e14 100%); }
        .dashboard-stats .stat-wallets .stat-accent { background: linear-gradient(90deg, #36b9cc 0%, #4e73df 100%); }
        .dashboard-stats .stat-balance .stat-accent { background: linear-gradient(90deg, #4e73df 0%, #0dcaf0 100%); }
        .dashboard-stats .stat-icon {
            font-size: 2.7rem;
            opacity: 0.85;
            min-width: 48px;
            text-align: center;
        }
        .dashboard-stats .stat-label {
            font-size: 1.05rem;
            color: #6c757d;
            font-weight: 500;
            margin-bottom: 0.15rem;
        }
        .dashboard-stats .stat-value {
            font-size: 2.1rem;
            font-weight: 700;
            color: #222;
            letter-spacing: 1px;
        }
        .dashboard-stats .chart-card {
            border-radius: 1.2rem;
            background: #fff;
            box-shadow: 0 2px 16px 0 rgba(60,72,100,.08), 0 1.5px 4px 0 rgba(60,72,100,.04);
        }
        @media (max-width: 767px) {
            .dashboard-stats .stat-value { font-size: 1.4rem; }
            .dashboard-stats .stat-label { font-size: 0.95rem; }
            .dashboard-stats .stat-icon { font-size: 2rem; }
        }
        body.dark-mode .dashboard-stats .stat-card, body.dark-mode .dashboard-stats .chart-card {
            background: #23272b;
            color: #f8f9fa;
        }
        body.dark-mode .dashboard-stats .stat-label { color: #adb5bd; }
        body.dark-mode .dashboard-stats .stat-value { color: #fff; }
    </style>
@endpush
@push('plugin-scripts')
    <script src="{{ asset('assets/plugins/chartjs/js/chart.js') }}"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function () {
            // Check if Chart is available
            if (typeof Chart === 'undefined') {
                console.error('Chart.js is not loaded. Please check the script path.');
                return;
            }

            const chartElement = document.getElementById('tripsChart');
            if (!chartElement) {
                console.error('Chart element not found');
                return;
            }

            const ctx = chartElement.getContext('2d');
            new Chart(ctx, {
                type: 'doughnut',
                data: {
                    labels: ['Completed', 'Cancelled', 'Ongoing'],
                    datasets: [{
                        data: [
                            {{ $completedTrips }},
                            {{ $cancelledTrips }},
                            {{ $ongoingTrips }}
                        ],
                        backgroundColor: [
                            'rgba(40, 167, 69, 0.85)', // green
                            'rgba(220, 53, 69, 0.85)', // red
                            'rgba(255, 193, 7, 0.85)'  // yellow
                        ],
                        borderColor: [
                            'rgba(40, 167, 69, 1)',
                            'rgba(220, 53, 69, 1)',
                            'rgba(255, 193, 7, 1)'
                        ],
                        borderWidth: 2
                    }]
                },
                options: {
                    responsive: true,
                    plugins: {
                        legend: {
                            position: 'bottom',
                            labels: {
                                font: { size: 16 }
                            }
                        }
                    }
                }
            });
        });
    </script>
@endpush
@endsection


@push('styles')

@endpush
