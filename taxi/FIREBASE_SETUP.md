# إعداد Firebase الآمن

## نظرة عامة
تم إنشاء نظام آمن لإدارة إعدادات Firebase بحيث لا يتم تخزين المفاتيح الحساسة في الملفات العامة.

## الملفات المُنشأة

### 1. Controller للتعامل مع إعدادات Firebase
- `app/Http/Controllers/FirebaseController.php`
- يحتوي على endpoint لإرجاع إعدادات Firebase من السيرفر

### 2. Service Worker محدث
- `public/firebase-messaging-sw.js`
- يستدعي إعدادات Firebase من السيرفر بدلاً من تخزينها محلياً

### 3. مدير Firebase للصفحات العادية
- `public/js/firebase-init.js`
- كلاس لإدارة Firebase في الصفحات العادية

### 4. مثال على الاستخدام
- `public/js/firebase-example.js`
- مثال على كيفية استخدام Firebase Manager

## خطوات الإعداد

### 1. إضافة متغيرات البيئة
أضف المتغيرات التالية إلى ملف `.env`:

```env
# Firebase Client Configuration
FIREBASE_API_KEY=your_api_key_here
FIREBASE_AUTH_DOMAIN=your_project.firebaseapp.com
FIREBASE_DATABASE_URL=https://your_project.firebaseio.com
FIREBASE_PROJECT_ID=your_project_id
FIREBASE_STORAGE_BUCKET=your_project.appspot.com
FIREBASE_MESSAGING_SENDER_ID=your_sender_id
FIREBASE_APP_ID=your_app_id
FIREBASE_MEASUREMENT_ID=your_measurement_id
FIREBASE_VAPID_KEY=your_vapid_key
```

### 2. الحصول على القيم من Firebase Console
1. اذهب إلى [Firebase Console](https://console.firebase.google.com)
2. اختر مشروعك
3. اذهب إلى Project Settings (أيقونة الترس)
4. في قسم "Your apps"، اختر تطبيق الويب
5. انسخ القيم من "Firebase SDK snippet"

### 3. إعداد VAPID Key للإشعارات
1. في Firebase Console، اذهب إلى Project Settings
2. اختر تبويب "Cloud Messaging"
3. في قسم "Web configuration"، انقر على "Generate key pair"
4. انسخ المفتاح وضعه في `FIREBASE_VAPID_KEY`

## كيفية الاستخدام

### في Service Worker
Service Worker سيحصل على الإعدادات تلقائياً من السيرفر عند التحميل.

### في الصفحات العادية
```html
<!-- في head الصفحة -->
<script type="module" src="/js/firebase-init.js"></script>
<script src="/js/firebase-example.js"></script>
```

```javascript
// في JavaScript الخاص بك
document.addEventListener('DOMContentLoaded', async () => {
    try {
        // طلب إذن الإشعارات والحصول على token
        const token = await window.firebaseManager.requestPermission();
        
        if (token) {
            console.log('FCM Token:', token);
            // أرسل token إلى السيرفر
        }
        
        // إعداد معالج الرسائل في المقدمة
        window.firebaseManager.setupForegroundMessageHandler((payload) => {
            console.log('رسالة جديدة:', payload);
        });
        
    } catch (error) {
        console.error('خطأ في إعداد Firebase:', error);
    }
});
```

## الأمان
- جميع المفاتيح محفوظة في متغيرات البيئة
- لا يتم تخزين أي مفاتيح في الملفات العامة
- Service Worker يحصل على الإعدادات من endpoint آمن
- يمكن إضافة middleware للتحكم في الوصول إلى endpoint الإعدادات

## ملاحظات مهمة
1. تأكد من أن ملف `.env` غير مُضاف إلى Git
2. في الإنتاج، تأكد من أن متغيرات البيئة محددة بشكل صحيح
3. يمكن إضافة cache للإعدادات لتحسين الأداء
4. تأكد من أن HTTPS مُفعل في الإنتاج لعمل Service Workers
