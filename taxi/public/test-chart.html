<!DOCTYPE html>
<html>
<head>
    <title>Chart.js Test</title>
</head>
<body>
    <h1>Chart.js Test</h1>
    <div id="results"></div>
    <canvas id="testChart" width="400" height="200"></canvas>
    
    <script src={{ asset('assets/plugins/chartjs/js/chart.js') }}></script>
    <script>
        const results = document.getElementById('results');
        
        // Test if Chart.js is loaded
        if (typeof Chart !== 'undefined') {
            results.innerHTML += '<p>✅ Chart.js loaded successfully</p>';
            results.innerHTML += `<p>📊 Chart.js version: ${Chart.version || 'Unknown'}</p>`;
            
            // Test creating a chart
            try {
                const ctx = document.getElementById('testChart').getContext('2d');
                const testChart = new Chart(ctx, {
                    type: 'doughnut',
                    data: {
                        labels: ['Test 1', 'Test 2', 'Test 3'],
                        datasets: [{
                            data: [10, 20, 30],
                            backgroundColor: [
                                'rgba(40, 167, 69, 0.85)',
                                'rgba(220, 53, 69, 0.85)',
                                'rgba(255, 193, 7, 0.85)'
                            ]
                        }]
                    },
                    options: {
                        responsive: true,
                        plugins: {
                            legend: {
                                position: 'bottom'
                            }
                        }
                    }
                });
                results.innerHTML += '<p>✅ Chart created successfully</p>';
            } catch (error) {
                results.innerHTML += `<p>❌ Error creating chart: ${error.message}</p>`;
            }
        } else {
            results.innerHTML += '<p>❌ Chart.js not loaded</p>';
            results.innerHTML += '<p>🔍 Checking script path...</p>';
            
            // Test if the script file exists
            fetch('/assets/plugins/chartjs/js/chart.js')
                .then(response => {
                    if (response.ok) {
                        results.innerHTML += '<p>✅ Chart.js file exists and accessible</p>';
                        results.innerHTML += '<p>⚠️ File loaded but Chart object not available - possible script error</p>';
                    } else {
                        results.innerHTML += `<p>❌ Chart.js file not accessible (${response.status})</p>`;
                    }
                })
                .catch(error => {
                    results.innerHTML += `<p>❌ Error checking Chart.js file: ${error.message}</p>`;
                });
        }
    </script>
</body>
</html>
