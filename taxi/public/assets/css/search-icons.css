/* Search input with Bootstrap Icons */
.search-input-wrapper {
    position: relative;
    display: inline-block;
    width: 100%;
}

.search-input-wrapper input {
    padding-right: 2.5rem;
}

.search-input-wrapper::after {
    content: "\F52A"; /* Bootstrap Icons search icon */
    font-family: "bootstrap-icons";
    position: absolute;
    right: 0.75rem;
    top: 50%;
    transform: translateY(-50%);
    pointer-events: none;
    color: #6c757d;
    font-size: 1rem;
}

/* Alternative using bi class */
.search-with-icon {
    position: relative;
}

.search-with-icon input {
    padding-right: 2.5rem;
}

.search-with-icon .bi-search {
    position: absolute;
    right: 0.75rem;
    top: 50%;
    transform: translateY(-50%);
    pointer-events: none;
    color: #6c757d;
}
