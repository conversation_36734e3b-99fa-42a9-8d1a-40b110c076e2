<!DOCTYPE html>
<html>
<head>
    <title>Firebase Test</title>
    <meta name="csrf-token" content="test-token">
</head>
<body>
    <h1>Firebase Configuration Test</h1>
    <div id="results"></div>
    
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://www.gstatic.com/firebasejs/8.10.1/firebase-app.js"></script>
    <script src="https://www.gstatic.com/firebasejs/8.10.1/firebase-messaging.js"></script>
    
    <script>
        // Test Firebase config
        const firebaseConfig = {
            apiKey: "AIzaSyAcdXn5P712MrHTfkH2Eewz8LAl0Twlt20",
            authDomain: "taxi-c0045.firebaseapp.com",
            databaseURL: "https://taxi-c0045-default-rtdb.firebaseio.com/",
            projectId: "taxi-c0045",
            storageBucket: "taxi-c0045.firebasestorage.app",
            messagingSenderId: "754590061481",
            appId: "1:754590061481:web:c21166049ca20c22a82ab8"
        };
        
        const vapidKey = "BFs6nTCwDsiEf4prMuELKPygyCh8Iw1RszhwfI52QMOr-vzsC3GY3CfkmMbqqs10YHS8bzuqKyTyu79qbrKYJC0";
        
        const results = document.getElementById('results');
        
        try {
            // Initialize Firebase
            firebase.initializeApp(firebaseConfig);
            results.innerHTML += '<p>✅ Firebase initialized successfully</p>';
            
            const messaging = firebase.messaging();
            results.innerHTML += '<p>✅ Firebase messaging initialized</p>';
            
            // Test service worker registration
            if ('serviceWorker' in navigator) {
                navigator.serviceWorker.register('/taxi/firebase-messaging-sw.js')
                    .then((registration) => {
                        results.innerHTML += '<p>✅ Service Worker registered successfully</p>';
                        messaging.useServiceWorker(registration);
                        
                        // Test notification permission
                        Notification.requestPermission().then((permission) => {
                            results.innerHTML += `<p>📱 Notification permission: ${permission}</p>`;
                            
                            if (permission === 'granted') {
                                messaging.getToken({ vapidKey: vapidKey })
                                    .then((currentToken) => {
                                        if (currentToken) {
                                            results.innerHTML += `<p>🔑 FCM Token: ${currentToken.substring(0, 50)}...</p>`;
                                        } else {
                                            results.innerHTML += '<p>❌ No registration token available</p>';
                                        }
                                    })
                                    .catch((err) => {
                                        results.innerHTML += `<p>❌ Error getting token: ${err.message}</p>`;
                                    });
                            }
                        });
                    })
                    .catch((error) => {
                        results.innerHTML += `<p>❌ Service Worker registration failed: ${error.message}</p>`;
                    });
            } else {
                results.innerHTML += '<p>❌ Service Worker not supported</p>';
            }
            
        } catch (error) {
            results.innerHTML += `<p>❌ Firebase initialization failed: ${error.message}</p>`;
        }
    </script>
</body>
</html>
