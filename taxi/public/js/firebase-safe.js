/**
 * Safe Firebase initialization with comprehensive error handling
 * This file provides a fallback for when Firebase config is not available
 */

(function() {
    'use strict';
    
    // Check if Firebase is available
    if (typeof firebase === 'undefined') {
        console.warn('Firebase SDK not loaded');
        return;
    }
    
    // Check if config is available
    if (!window.firebaseConfig) {
        console.warn('Firebase config not found in window.firebaseConfig');
        return;
    }
    
    // Validate config
    const requiredFields = ['apiKey', 'authDomain', 'projectId', 'messagingSenderId', 'appId'];
    const missingFields = requiredFields.filter(field => !window.firebaseConfig[field]);
    
    if (missingFields.length > 0) {
        console.error('Missing Firebase config fields:', missingFields);
        return;
    }
    
    try {
        // Initialize Firebase
        firebase.initializeApp(window.firebaseConfig);
        console.log('✅ Firebase initialized successfully');
        
        // Initialize messaging if available
        if (firebase.messaging && firebase.messaging.isSupported()) {
            const messaging = firebase.messaging();
            console.log('✅ Firebase messaging initialized');
            
            // Register service worker
            if ('serviceWorker' in navigator) {
                // Try multiple paths for service worker
                const swPaths = [
                    
                    '/firebase-messaging-sw.js',
                    './firebase-messaging-sw.js',
                    '/taxi/firebase-messaging-sw.js'
                ];

                async function registerServiceWorker() {
                    for (const path of swPaths) {
                        try {
                            console.log(`🔄 Trying to register service worker: ${path}`);
                            const registration = await navigator.serviceWorker.register(path);
                            console.log('✅ Service Worker registered:', registration);
                            messaging.useServiceWorker(registration);
                            return registration;
                        } catch (error) {
                            console.warn(`⚠️ Failed to register service worker at ${path}:`, error.message);
                        }
                    }
                    throw new Error('Failed to register service worker at any path');
                }

                registerServiceWorker()
                    .then(() => {
                        // Request permission and get token
                        requestNotificationPermission(messaging);
                    })
                    .catch((error) => {
                        console.error('❌ Service Worker registration failed:', error);
                    });
            } else {
                console.warn('⚠️ Service Worker not supported');
            }
            
            // Handle foreground messages
            messaging.onMessage((payload) => {
                console.log('📨 Foreground message received:', payload);
                handleForegroundMessage(payload);
            });
            
        } else {
            console.warn('⚠️ Firebase messaging not supported');
        }
        
    } catch (error) {
        console.error('❌ Firebase initialization failed:', error);
    }
    
    /**
     * Request notification permission and get FCM token
     */
    function requestNotificationPermission(messaging) {
        console.log('🔄 Starting notification permission request...');

        // Check VAPID key
        if (!window.vapidKey) {
            console.warn('⚠️ VAPID key not found in window.vapidKey');
            console.log('Available window properties:', Object.keys(window).filter(k => k.includes('vapid') || k.includes('firebase')));
            return;
        }

        console.log('✅ VAPID key found:', window.vapidKey.substring(0, 20) + '...');

        Notification.requestPermission().then((permission) => {
            console.log('🔔 Notification permission:', permission);

            if (permission === 'granted') {
                console.log('🔄 Requesting FCM token...');

                // Add timeout to prevent hanging
                const tokenPromise = messaging.getToken({ vapidKey: window.vapidKey });
                const timeoutPromise = new Promise((_, reject) => {
                    setTimeout(() => reject(new Error('Token request timeout')), 10000);
                });

                Promise.race([tokenPromise, timeoutPromise])
                    
                    .then((currentToken) => {                        
                        if (currentToken) {
                            console.log('🔑 FCM Token received successfully');
                            console.log('Token length:', currentToken.length);
                            sendTokenToServer(currentToken);
                        } else {
                            console.log('⚠️ No registration token available');
                            console.log('This may happen if:');
                            console.log('- Notifications are blocked');
                            console.log('- App is not focused');
                            console.log('- Service worker registration failed');
                        }
                    })
                    .catch((err) => {
                        console.error('❌ Error getting token:', err);
                        console.error('Error details:', {
                            name: err.name,
                            message: err.message,
                            code: err.code,
                            stack: err.stack
                        });

                        // Try alternative approach
                        console.log('🔄 Trying alternative token request...');
                        tryAlternativeTokenRequest(messaging);
                    });
            } else {
                console.log('🚫 Notification permission denied');
            }
        }).catch((error) => {
            console.error('❌ Error requesting permission:', error);
        });
    }

    /**
     * Alternative token request method
     */
    function tryAlternativeTokenRequest(messaging) {
        console.log('🔄 Attempting alternative token request without VAPID...');

        messaging.getToken()
            .then((currentToken) => {
                if (currentToken) {
                    console.log('🔑 FCM Token received via alternative method');
                    sendTokenToServer(currentToken);
                } else {
                    console.log('⚠️ Alternative method also failed to get token');
                }
            })
            .catch((err) => {
                console.error('❌ Alternative token request also failed:', err);
                console.log('💡 Possible solutions:');
                console.log('1. Check Firebase project configuration');
                console.log('2. Verify VAPID key in Firebase Console');
                console.log('3. Ensure HTTPS is used');
                console.log('4. Check browser compatibility');
            });
    }
    
    /**
     * Send FCM token to server
     */
    function sendTokenToServer(token) {
        if (typeof $ === 'undefined') {
            console.warn('⚠️ jQuery not available, cannot send token');
            return;
        }
            console.log(token);
            
        const csrfToken = $('meta[name="csrf-token"]').attr('content');
        if (!csrfToken) {
            console.warn('⚠️ CSRF token not found');
            return;
        }
        
        $.ajax({
            url: "/taxi/update-device-token",
            method: "PUT",
            data: {
                token: token,
                platform: "web",
            },
            headers: {
                "X-CSRF-TOKEN": csrfToken,
            },
            success: function (response) {
                console.log('✅ Token sent to server successfully');
                localStorage.setItem("isCmfToken", true);
            },
            error: function(xhr, status, error) {
                console.error('❌ Failed to send token to server:', error);
            }
        });
    }
    
    /**
     * Handle foreground messages
     */
    function handleForegroundMessage(payload) {
        const notificationLang = localStorage.getItem("notificationLang") || "en";
        
        const title = (payload.data && payload.data[`title_${notificationLang}`]) 
            ? payload.data[`title_${notificationLang}`]
            : (payload.notification ? payload.notification.title : 'New Notification');
            
        const body = (payload.data && payload.data[`body_${notificationLang}`]) 
            ? payload.data[`body_${notificationLang}`]
            : (payload.notification ? payload.notification.body : '');
        
        const senderName = (payload.data && payload.data.sender_name) 
            ? payload.data.sender_name 
            : "System";
            
        const notificationId = (payload.data && payload.data.notification_id) 
            ? payload.data.notification_id 
            : Date.now().toString();
        
        // Update notification count
        updateNotificationCount();
        
        // Add to dropdown if function exists
        if (typeof addNotificationToDropdown === 'function') {
            setTimeout(() => {
                addNotificationToDropdown({
                    id: notificationId,
                    sender_name: senderName,
                    title: title,
                    body: body,
                    created_at_human: "just now",
                });
            }, 1000);
        }
    }
    
    /**
     * Update notification count
     */
    function updateNotificationCount() {
        if (typeof $ === 'undefined') return;
        
        const $count = $("#countUnreadMessages");
        if ($count.length) {
            const current = parseInt($count.text()) || 0;
            $count.text(current + 1).addClass("badge-notify");
        }
    }
    
})();
