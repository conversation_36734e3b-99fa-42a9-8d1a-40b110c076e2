// Example usage of Firebase Manager
document.addEventListener('DOMContentLoaded', async () => {
    try {
        // Initialize Firebase
        await window.firebaseManager.initialize();

        // Request notification permission and get token
        const token = await window.firebaseManager.requestPermission();
        
        if (token) {
            // Send token to your server to store it
            await sendTokenToServer(token);
        }

        // Setup foreground message handler
        window.firebaseManager.setupForegroundMessageHandler((payload) => {
            // Handle foreground messages
            console.log('Received foreground message:', payload);
            
            // Show custom notification or update UI
            showCustomNotification(payload);
        });

    } catch (error) {
        console.error('Error setting up Firebase:', error);
    }
});

// Function to send token to server
async function sendTokenToServer(token) {
    try {
        const response = await fetch('/api/fcm-token', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            },
            body: JSON.stringify({ token: token })
        });

        if (response.ok) {
            console.log('Token sent to server successfully');
        } else {
            console.error('Failed to send token to server');
        }
    } catch (error) {
        console.error('Error sending token to server:', error);
    }
}

// Function to show custom notification
function showCustomNotification(payload) {
    // Create custom notification UI
    const notification = document.createElement('div');
    notification.className = 'firebase-notification';
    notification.innerHTML = `
        <div class="notification-content">
            <h4>${payload.notification.title}</h4>
            <p>${payload.notification.body}</p>
            <button onclick="this.parentElement.parentElement.remove()">Close</button>
        </div>
    `;
    
    // Add styles
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: white;
        border: 1px solid #ddd;
        border-radius: 8px;
        padding: 15px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        z-index: 1000;
        max-width: 300px;
    `;
    
    document.body.appendChild(notification);
    
    // Auto remove after 5 seconds
    setTimeout(() => {
        if (notification.parentElement) {
            notification.remove();
        }
    }, 5000);
}
