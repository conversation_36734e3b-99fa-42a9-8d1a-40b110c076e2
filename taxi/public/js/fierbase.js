// Firebase config will be injected from <PERSON><PERSON>
const firebaseConfig = window.firebaseConfig;
const vapidKey = window.vapidKey;

// Check if Firebase config is available
if (!firebaseConfig) {
    console.error('Firebase config not found. Make sure Firebase is properly configured.');
} else {
    try {
        // Initialize Firebase
        firebase.initializeApp(firebaseConfig);
        const messaging = firebase.messaging();

        // Register service worker
        if ('serviceWorker' in navigator) {
            navigator.serviceWorker.register('/taxi/firebase-messaging-sw.js')
                .then((registration) => {
                    console.log('Service Worker registered successfully:', registration);
                    messaging.useServiceWorker(registration);
                })
                .catch((error) => {
                    console.error('Service Worker registration failed:', error);
                });
        }

        // Request notification permission
        Notification.requestPermission().then((permission) => {
            console.log('Notification permission:', permission);

            if (permission === 'granted') {
                messaging
                    .getToken({
                        vapidKey: vapidKey,
                    })
                    .then((currentToken) => {
                        if (currentToken) {
                            console.log('FCM Token:', currentToken);
                            $.ajax({
                                url: "/taxi/update-device-token",
                                method: "put",
                                data: {
                                    token: currentToken,
                                    platform: "web",
                                },
                                success: function (response) {
                                    localStorage.setItem("isCmfToken", true);
                                    console.log('Token updated successfully:', response);
                                },
                                error: function(xhr, status, error) {
                                    console.error('Failed to update token:', error);
                                },
                                headers: {
                                    "X-CSRF-TOKEN": $('meta[name="csrf-token"]').attr("content"),
                                },
                            });
                        } else {
                            console.log('No registration token available.');
                        }
                    })
                    .catch((err) => {
                        console.error("An error occurred while retrieving token:", err);
                    });
            } else {
                console.log('Notification permission denied.');
            }
        }).catch((error) => {
            console.error('Error requesting notification permission:', error);
        });

        // Handle foreground messages
        messaging.onMessage(function (payload) {
            let notificationLang = localStorage.getItem("notificationLang") || "en";
            const title =
                payload.data && payload.data[`title_${notificationLang}`]
                    ? payload.data[`title_${notificationLang}`]
                    : payload.notification.title || "";
            const body =
                payload.data && payload.data[`body_${notificationLang}`]
                    ? payload.data[`body_${notificationLang}`]
                    : payload.notification.body || "";

            const senderName =
                payload.data && payload.data.sender_name
                    ? payload.data.sender_name
                    : "Unknown";
            const notification_id =
                payload.data && payload.data.notification_id
                    ? payload.data.notification_id
                    : "Unknown";

            setTimeout(function () {
                var newNotification = {
                    id: notification_id,
                    sender_name: senderName,
                    title: title,
                    body: body,
                    created_at_human: "just now",
                };
                var $count = $("#countUnreadMessages");
                var current = parseInt($count.text()) || 0;
                $count.text(current + 1).addClass("badge-notify");
                addNotificationToDropdown(newNotification);
            }, 5000);
        });

    } catch (error) {
        console.error('Error initializing Firebase:', error);
    }
}
messaging.onMessage(function (payload) {
    let notificationLang = localStorage.getItem("notificationLang") || "en";
    const title =
        payload.data && payload.data[`title_${notificationLang}`]
            ? payload.data[`title_${notificationLang}`]
            : payload.notification.title || "";
    const body =
        payload.data && payload.data[`body_${notificationLang}`]
            ? payload.data[`body_${notificationLang}`]
            : payload.notification.body || "";

    const senderName =
        payload.data && payload.data.sender_name
            ? payload.data.sender_name
            : "Unknown";
    const notification_id =
        payload.data && payload.data.notification_id
            ? payload.data.notification_id
            : "Unknown";

    setTimeout(function () {
        var newNotification = {
            id: notification_id,
            sender_name: senderName,
            title: title,
            body: body,
            created_at_human: "just now",
        };
        var $count = $("#countUnreadMessages");
        var current = parseInt($count.text()) || 0;
        $count.text(current + 1).addClass("badge-notify");
        addNotificationToDropdown(newNotification);
    }, 5000);

    // const { title, body } = payload.notification; // This line is removed as per the new_code
});
function addNotificationToDropdown(notification) {
    var html = `
        <div>
            <a class="dropdown-item border-bottom py-2 notification-item unread-notification" href="javascript:;" data-id="${notification.id}">
                <div class="d-flex align-items-center gap-3">
                    <div class="">
                        <img src="https://ui-avatars.com/api/?name=${notification.sender_name}&background=random" width="45" height="45" alt="${notification.sender_name}" class="rounded-circle">
                    </div>
                    <div class="">
                        <h5 class="notify-title">${notification.title} <span class='badge bg-primary ms-2' style='font-size:10px;vertical-align:middle;'>new</span></h5>
                        <p class="mb-0 notify-desc">${notification.body}</p>
                        <p class="mb-0 notify-time">${notification.created_at_human}</p>
                    </div>
                    <div class="notify-close position-absolute end-0 me-3">
                        <i class="material-icons-outlined fs-6">close</i>
                    </div>
                </div>
            </a>
        </div>
    `;
    $(".notify-list").prepend(html);
}

$(document).on("click", ".notification-item", function () {
    $(this).find(".badge.bg-primary").remove();
    $(this).removeClass("unread-notification");
});
