/**
 * Force Local Assets - Block CDN requests and use local alternatives
 * This script prevents loading of external CDN resources and forces local alternatives
 */

(function() {
    'use strict';
    
    // Block CDN requests by intercepting fetch and XMLHttpRequest
    const originalFetch = window.fetch;
    const originalXHROpen = XMLHttpRequest.prototype.open;
    
    // List of blocked CDN domains
    const blockedDomains = [
        'cdn.jsdelivr.net',
        'cdnjs.cloudflare.com',
        'unpkg.com'
    ];
    
    // Local alternatives mapping
    const localAlternatives = {
        // 'bootstrap-icons': '/assets/css/bootstrap-icons.css',
        'chart.js': '/assets/plugins/chartjs/js/chart.js',
        'datatables': '/assets/plugins/datatables/js/jquery.dataTables.min.js'
    };
    
    // Override fetch
    window.fetch = function(url, options) {
        if (typeof url === 'string') {
            for (const domain of blockedDomains) {
                if (url.includes(domain)) {
                    console.warn(`🚫 Blocked CDN request: ${url}`);
                    
                    // Try to find local alternative
                    for (const [key, localPath] of Object.entries(localAlternatives)) {
                        if (url.includes(key)) {
                            console.log(`✅ Using local alternative: ${localPath}`);
                            return originalFetch.call(this, localPath, options);
                        }
                    }
                    
                    // Return rejected promise for blocked requests
                    return Promise.reject(new Error(`CDN request blocked: ${url}`));
                }
            }
        }
        
        return originalFetch.call(this, url, options);
    };
    
    // Override XMLHttpRequest
    XMLHttpRequest.prototype.open = function(method, url, async, user, password) {
        if (typeof url === 'string') {
            for (const domain of blockedDomains) {
                if (url.includes(domain)) {
                    console.warn(`🚫 Blocked XHR CDN request: ${url}`);
                    
                    // Try to find local alternative
                    for (const [key, localPath] of Object.entries(localAlternatives)) {
                        if (url.includes(key)) {
                            console.log(`✅ Using local alternative: ${localPath}`);
                            return originalXHROpen.call(this, method, localPath, async, user, password);
                        }
                    }
                    
                    // Block the request
                    throw new Error(`CDN request blocked: ${url}`);
                }
            }
        }
        
        return originalXHROpen.call(this, method, url, async, user, password);
    };
    
    // Block dynamic link/script creation for CDN resources
    const originalCreateElement = document.createElement;
    document.createElement = function(tagName) {
        const element = originalCreateElement.call(this, tagName);
        
        if (tagName.toLowerCase() === 'link' || tagName.toLowerCase() === 'script') {
            const originalSetAttribute = element.setAttribute;
            element.setAttribute = function(name, value) {
                if ((name === 'href' || name === 'src') && typeof value === 'string') {
                    for (const domain of blockedDomains) {
                        if (value.includes(domain)) {
                            console.warn(`🚫 Blocked dynamic ${tagName} CDN: ${value}`);
                            
                            // Try to find local alternative
                            for (const [key, localPath] of Object.entries(localAlternatives)) {
                                if (value.includes(key)) {
                                    console.log(`✅ Using local alternative: ${localPath}`);
                                    return originalSetAttribute.call(this, name, localPath);
                                }
                            }
                            
                            // Don't set the attribute for blocked resources
                            return;
                        }
                    }
                }
                
                return originalSetAttribute.call(this, name, value);
            };
        }
        
        return element;
    };
    
    // Monitor and report blocked requests
    let blockedCount = 0;
    const reportBlocked = () => {
        if (blockedCount > 0) {
            console.log(`📊 Total CDN requests blocked: ${blockedCount}`);
        }
    };
    
    // Report after page load
    window.addEventListener('load', () => {
        setTimeout(reportBlocked, 2000);
    });
    
    console.log('🛡️ CDN blocker initialized - forcing local assets');
    
})();
