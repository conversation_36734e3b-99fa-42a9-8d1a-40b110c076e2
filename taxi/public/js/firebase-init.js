// Firebase initialization for main application
import { initializeApp } from 'firebase/app';
import { getMessaging, getToken, onMessage } from 'firebase/messaging';

class FirebaseManager {
    constructor() {
        this.app = null;
        this.messaging = null;
        this.initialized = false;
    }

    async initialize() {
        if (this.initialized) {
            return this.messaging;
        }

        try {
            // Fetch Firebase config from server
            const response = await fetch('/firebase-config');
            const data = await response.json();

            if (!data.config) {
                throw new Error('Firebase config not found');
            }

            // Initialize Firebase
            this.app = initializeApp(data.config);
            this.messaging = getMessaging(this.app);
            this.vapidKey = data.vapidKey;
            this.initialized = true;

            console.log('Firebase initialized successfully');
            return this.messaging;
        } catch (error) {
            console.error('Error initializing Firebase:', error);
            throw error;
        }
    }

    async requestPermission() {
        if (!this.initialized) {
            await this.initialize();
        }

        try {
            const permission = await Notification.requestPermission();
            if (permission === 'granted') {
                console.log('Notification permission granted.');
                return await this.getToken();
            } else {
                console.log('Unable to get permission to notify.');
                return null;
            }
        } catch (error) {
            console.error('Error requesting notification permission:', error);
            return null;
        }
    }

    async getToken() {
        if (!this.initialized) {
            await this.initialize();
        }

        try {
            const token = await getToken(this.messaging, {
                vapidKey: this.vapidKey
            });
            
            if (token) {
                console.log('FCM Token:', token);
                return token;
            } else {
                console.log('No registration token available.');
                return null;
            }
        } catch (error) {
            console.error('An error occurred while retrieving token:', error);
            return null;
        }
    }

    setupForegroundMessageHandler(callback) {
        if (!this.initialized) {
            console.error('Firebase not initialized. Call initialize() first.');
            return;
        }

        onMessage(this.messaging, (payload) => {
            console.log('Message received in foreground:', payload);
            if (callback && typeof callback === 'function') {
                callback(payload);
            }
        });
    }
}

// Create global instance
window.firebaseManager = new FirebaseManager();

// Auto-initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', async () => {
    try {
        await window.firebaseManager.initialize();
    } catch (error) {
        console.error('Failed to auto-initialize Firebase:', error);
    }
});
