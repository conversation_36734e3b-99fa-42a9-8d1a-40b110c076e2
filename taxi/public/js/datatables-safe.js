/**
 * Safe DataTables initialization
 * Fallback for when CDN DataTables is blocked by CSP
 */

(function() {
    'use strict';
    
    // Check if DataTables is already loaded
    if (typeof $.fn.DataTable !== 'undefined') {
        console.log('✅ DataTables already loaded');
        return;
    }
    
    // Try to load DataTables from local assets
    const scripts = [
        '/assets/plugins/datatables/js/jquery.dataTables.min.js',
        '/assets/plugins/datatables/js/dataTables.bootstrap5.min.js',
        '/assets/plugins/datatables/js/dataTables.responsive.min.js'
    ];
    
    const styles = [
        '/assets/plugins/datatables/css/dataTables.bootstrap5.min.css',
        '/assets/plugins/datatables/css/responsive.bootstrap5.min.css'
    ];
    
    // Load styles first
    styles.forEach(href => {
        if (!document.querySelector(`link[href="${href}"]`)) {
            const link = document.createElement('link');
            link.rel = 'stylesheet';
            link.href = href;
            document.head.appendChild(link);
        }
    });
    
    // Load scripts sequentially
    loadScriptsSequentially(scripts, 0);
    
    function loadScriptsSequentially(scripts, index) {
        if (index >= scripts.length) {
            console.log('✅ All DataTables scripts loaded');
            return;
        }
        
        const script = document.createElement('script');
        script.src = scripts[index];
        script.onload = () => {
            console.log(`✅ Loaded: ${scripts[index]}`);
            loadScriptsSequentially(scripts, index + 1);
        };
        script.onerror = () => {
            console.warn(`⚠️ Failed to load: ${scripts[index]}`);
            loadScriptsSequentially(scripts, index + 1);
        };
        document.head.appendChild(script);
    }
    
})();

/**
 * Safe DataTable initialization function
 */
window.initSafeDataTable = function(selector, options = {}) {
    const $table = $(selector);
    if ($table.length === 0) {
        console.warn(`⚠️ Table not found: ${selector}`);
        return null;
    }
    
    // Wait for DataTables to be available
    const maxAttempts = 50;
    let attempts = 0;
    
    const tryInit = () => {
        attempts++;
        
        if (typeof $.fn.DataTable !== 'undefined') {
            try {
                const defaultOptions = {
                    responsive: true,
                    pageLength: 10,
                    language: {
                        search: "البحث:",
                        lengthMenu: "عرض _MENU_ عنصر",
                        info: "عرض _START_ إلى _END_ من _TOTAL_ عنصر",
                        paginate: {
                            first: "الأول",
                            last: "الأخير",
                            next: "التالي",
                            previous: "السابق"
                        },
                        emptyTable: "لا توجد بيانات",
                        zeroRecords: "لم يتم العثور على نتائج"
                    }
                };
                
                const finalOptions = { ...defaultOptions, ...options };
                const dataTable = $table.DataTable(finalOptions);
                console.log(`✅ DataTable initialized: ${selector}`);
                return dataTable;
            } catch (error) {
                console.error(`❌ DataTable initialization failed: ${selector}`, error);
                return null;
            }
        } else if (attempts < maxAttempts) {
            setTimeout(tryInit, 100);
        } else {
            console.error(`❌ DataTables not available after ${maxAttempts} attempts`);
            return null;
        }
    };
    
    return tryInit();
};
