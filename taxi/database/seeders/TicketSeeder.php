<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Ticket;
use App\Models\User;
use App\Models\TicketCategory;
class TicketSeeder extends Seeder
{
    public function run()
    {
        $users = User::all();

        if ($users->isEmpty()) {
            // Create a test user if none exist
            $user = User::create([
                'name' => 'Test User',
                'email' => '<EMAIL>',
                'password' => bcrypt('password'),
                'phone' => '**********'
            ]);
            $users = collect([$user]);
        }

        $tickets = [
            [
                'ticket_number' => 'TKT-' . str_pad(1, 6, '0', STR_PAD_LEFT),
                'title' => 'Login Issue',
                'description' => 'Unable to login to my account',
                'priority' => 'high',
                'status' => 'open',
                'category_id' =>TicketCategory::where('slug', 'technical-support')->first()->id,
                'sender_type' => 'App\Models\User',
                'sender_id' => $users->first()->id,
                'sender_name' => $users->first()->name,
                'sender_email' => $users->first()->email,
                'sender_phone' => $users->first()->phone,
            ],
            [
                'ticket_number' => 'TKT-' . str_pad(2, 6, '0', STR_PAD_LEFT),
                'title' => 'Payment Problem',
                'description' => 'Payment was deducted but trip not confirmed',
                'priority' => 'urgent',
                'status' => 'pending',
                'category_id' =>TicketCategory::where('slug', 'payment-issues')->first()->id,
                'sender_type' => 'App\Models\User',
                'sender_id' => $users->first()->id,
                'sender_name' => $users->first()->name,
                'sender_email' => $users->first()->email,
                'sender_phone' => $users->first()->phone,
            ],
            [
                'ticket_number' => 'TKT-' . str_pad(3, 6, '0', STR_PAD_LEFT),
                'title' => 'App Crash',
                'description' => 'App crashes when trying to book a ride',
                'priority' => 'medium',
                'status' => 'in_progress',
                'category_id' =>TicketCategory::where('slug', 'technical-support')->first()->id,
                'sender_type' => 'App\Models\User',
                'sender_id' => $users->first()->id,
                'sender_name' => $users->first()->name,
                'sender_email' => $users->first()->email,
                'sender_phone' => $users->first()->phone,
            ],
            [
                'ticket_number' => 'TKT-' . str_pad(4, 6, '0', STR_PAD_LEFT),
                'title' => 'Driver Complaint',
                'description' => 'Driver was rude and unprofessional',
                'priority' => 'low',
                'status' => 'resolved',
                'category_id' =>TicketCategory::where('slug', 'account-issues')->first()->id,
                'sender_type' => 'App\Models\User',
                'sender_id' => $users->first()->id,
                'sender_name' => $users->first()->name,
                'sender_email' => $users->first()->email,
                'sender_phone' => $users->first()->phone,
                'resolved_at' => now()->subDays(1),
            ],
            [
                'ticket_number' => 'TKT-' . str_pad(5, 6, '0', STR_PAD_LEFT),
                'title' => 'Feature Request',
                'description' => 'Please add dark mode to the app',
                'priority' => 'low',
                'status' => 'closed',
                'category_id' =>TicketCategory::where('slug', 'feature-request')->first()->id,
                'sender_type' => 'App\Models\User',
                'sender_id' => $users->first()->id,
                'sender_name' => $users->first()->name,
                'sender_email' => $users->first()->email,
                'sender_phone' => $users->first()->phone,
                'closed_at' => now()->subHours(2),
            ]
        ];

        foreach ($tickets as $ticketData) {
            Ticket::create($ticketData);
        }
    }
}
