<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('email_notfictions', function (Blueprint $table) {
            $table->id();
            $table->string("title");
            $table->string("body");
            $table->timestamps();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('email_notfictions');
    }
};
