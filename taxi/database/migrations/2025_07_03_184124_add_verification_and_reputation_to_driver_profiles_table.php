<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('driver_profiles', function (Blueprint $table) {
            $table->boolean('is_driver_verified')->default(false)->after('vehicle_license_back');
            $table->text('verification_note')->nullable()->after('is_driver_verified');
            $table->float('reputation_score')->default(5.0)->after('verification_note');
            $table->text('vehicle_info')->nullable()->after('reputation_score'); // or use ->json() if it's structured
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('driver_profiles', function (Blueprint $table) {
             $table->dropColumn([
                'is_driver_verified',
                'verification_note',
                'reputation_score',
                'vehicle_info'
            ]);
        });
    }
};
