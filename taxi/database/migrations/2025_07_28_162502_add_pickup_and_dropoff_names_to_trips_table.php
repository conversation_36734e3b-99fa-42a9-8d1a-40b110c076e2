<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{

    public function up(): void
    {
        Schema::table('trips', function (Blueprint $table) {
            $table->string('pickup_name')->nullable()->after('pickup_lng');
            $table->string('dropoff_name')->nullable()->after('dropoff_lng');
        });
    }

    public function down(): void
    {
        Schema::table('trips', function (Blueprint $table) {
            $table->dropColumn(['pickup_name', 'dropoff_name']);
        });
    }
};
