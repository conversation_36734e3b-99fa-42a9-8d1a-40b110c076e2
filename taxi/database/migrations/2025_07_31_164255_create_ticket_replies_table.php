<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('ticket_replies', function (Blueprint $table) {
            $table->id();
            $table->foreignId('ticket_id')->constrained()->onDelete('cascade');

            // Polymorphic relationship for replier
            $table->morphs('replier'); // Creates replier_type and replier_id
            $table->string('replier_name')->nullable(); // For guests
            $table->string('replier_email')->nullable(); // For guests

            $table->text('message');
            $table->boolean('is_internal')->default(false); // Internal notes for admins only
            $table->json('attachments')->nullable();
            $table->timestamps();

            $table->index(['ticket_id', 'created_at']);
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('ticket_replies');
    }
};
