# نظام إدارة التذاكر والدعم الفني

نظام شامل لإدارة التذاكر والدعم الفني مبني بـ Laravel مع دعم الإشعارات الفورية عبر Firebase.

## 📋 المحتويات

- [المتطلبات](#المتطلبات)
- [التثبيت](#التثبيت)
- [الإعداد](#الإعداد)
- [الميزات](#الميزات)
- [الاستخدام](#الاستخدام)
- [API](#api)
- [المساهمة](#المساهمة)

## 🔧 المتطلبات

- PHP >= 8.2
- Composer
- Node.js >= 16
- MySQL >= 8.0
- Redis (اختياري للـ cache)

## 🚀 التثبيت

### 1. استنساخ المشروع

```bash
git clone https://github.com/your-username/ticket-system.git
cd ticket-system
```

### 2. تثبيت التبعيات

```bash
# تثبيت تبعيات PHP
composer install

# تثبيت تبعيات JavaScript
npm install
```

### 3. إعداد البيئة

```bash
# نسخ ملف البيئة
cp .env.example .env

# توليد مفتاح التطبيق
php artisan key:generate
```

### 4. إعداد قاعدة البيانات

```bash
# تشغيل الهجرات
php artisan migrate

# تشغيل البذور (اختياري)
php artisan db:seed
```

### 5. بناء الأصول

```bash
# للتطوير
npm run dev

# للإنتاج
npm run build
```

## ⚙️ الإعداد

### إعداد قاعدة البيانات

قم بتحديث ملف `.env` بمعلومات قاعدة البيانات:

```env
DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=ticket_system
DB_USERNAME=your_username
DB_PASSWORD=your_password
```

### إعداد Firebase

أضف معلومات Firebase إلى ملف `.env`:

```env
FIREBASE_API_KEY=your_api_key
FIREBASE_AUTH_DOMAIN=your_project.firebaseapp.com
FIREBASE_DATABASE_URL=https://your_project-default-rtdb.firebaseio.com
FIREBASE_PROJECT_ID=your_project_id
FIREBASE_STORAGE_BUCKET=your_project.firebasestorage.app
FIREBASE_MESSAGING_SENDER_ID=your_sender_id
FIREBASE_APP_ID=your_app_id
FIREBASE_MEASUREMENT_ID=your_measurement_id
FIREBASE_VAPID_KEY=your_vapid_key
```

### إعداد البريد الإلكتروني

```env
MAIL_MAILER=smtp
MAIL_HOST=your_smtp_host
MAIL_PORT=587
MAIL_USERNAME=your_email
MAIL_PASSWORD=your_password
MAIL_ENCRYPTION=tls
MAIL_FROM_ADDRESS=<EMAIL>
MAIL_FROM_NAME="${APP_NAME}"
```

## ✨ الميزات

### 🎫 إدارة التذاكر
- إنشاء وتتبع التذاكر
- نظام أولويات (منخفضة، متوسطة، عالية، عاجلة)
- حالات التذاكر (مفتوحة، قيد المعالجة، مغلقة)
- تصنيف التذاكر حسب الأقسام

### 👥 إدارة المستخدمين
- نظام أدوار متقدم (Admin, Agent, User)
- ملفات شخصية للمستخدمين
- إدارة الصلاحيات

### 💬 نظام الردود
- ردود داخلية للموظفين
- ردود عامة للعملاء
- دعم المرفقات
- نظام العلاقات المتعددة (Polymorphic)

### 🔔 الإشعارات
- إشعارات فورية عبر Firebase
- إشعارات البريد الإلكتروني
- إشعارات داخل النظام

### 📊 التقارير والإحصائيات
- تقارير شاملة عن الأداء
- إحصائيات التذاكر
- تحليلات الاستجابة

## 🎯 الاستخدام

### تشغيل الخادم

```bash
# تشغيل خادم Laravel
php artisan serve

# تشغيل Vite للتطوير
npm run dev
```

### إنشاء مستخدم إداري

```bash
php artisan make:admin
```

### تشغيل المهام المجدولة

```bash
# إضافة إلى crontab
* * * * * cd /path-to-your-project && php artisan schedule:run >> /dev/null 2>&1
```

### تشغيل الطوابير

```bash
php artisan queue:work
```

## 🔗 API

### المصادقة

```http
POST /api/login
Content-Type: application/json

{
    "email": "<EMAIL>",
    "password": "password"
}
```

### التذاكر

```http
# جلب جميع التذاكر
GET /api/tickets

# إنشاء تذكرة جديدة
POST /api/tickets
Content-Type: application/json

{
    "title": "عنوان التذكرة",
    "description": "وصف المشكلة",
    "priority": "high",
    "category_id": 1
}

# جلب تذكرة محددة
GET /api/tickets/{id}

# تحديث تذكرة
PUT /api/tickets/{id}

# حذف تذكرة
DELETE /api/tickets/{id}
```

### الردود

```http
# إضافة رد على تذكرة
POST /api/tickets/{ticket_id}/replies
Content-Type: application/json

{
    "message": "نص الرد",
    "is_internal": false
}
```

## 🗂️ هيكل المشروع

```
├── app/
│   ├── Http/Controllers/     # المتحكمات
│   ├── Models/              # النماذج
│   ├── Helpers/             # المساعدات
│   └── Services/            # الخدمات
├── database/
│   ├── migrations/          # هجرات قاعدة البيانات
│   └── seeders/            # بذور البيانات
├── resources/
│   ├── views/              # قوالب Blade
│   ├── js/                 # ملفات JavaScript
│   └── css/                # ملفات CSS
├── public/
│   ├── js/                 # ملفات JavaScript العامة
│   └── firebase-messaging-sw.js
├── config/
│   └── firebase-client.php  # إعدادات Firebase
└── routes/
    ├── web.php             # مسارات الويب
    └── api.php             # مسارات API
```

## 🛠️ التطوير

### إضافة ميزة جديدة

1. إنشاء Migration:
```bash
php artisan make:migration create_new_feature_table
```

2. إنشاء Model:
```bash
php artisan make:model NewFeature
```

3. إنشاء Controller:
```bash
php artisan make:controller NewFeatureController --resource
```

4. إضافة المسارات في `routes/web.php`

### اختبار الكود

```bash
# تشغيل الاختبارات
php artisan test

# تشغيل اختبارات محددة
php artisan test --filter=TicketTest
```

## 🔒 الأمان

- حماية CSRF على جميع النماذج
- تشفير كلمات المرور باستخدام bcrypt
- تنظيف المدخلات من XSS
- نظام صلاحيات متقدم
- حماية API بـ Sanctum

## 📈 الأداء

- استخدام Redis للـ cache
- تحسين استعلامات قاعدة البيانات
- ضغط الأصول
- Lazy Loading للعلاقات

## 🐛 استكشاف الأخطاء

### مشاكل شائعة

1. **خطأ في Firebase:**
```bash
# تأكد من صحة إعدادات Firebase في .env
php artisan config:clear
```

2. **مشاكل الصلاحيات:**
```bash
# إعطاء صلاحيات للمجلدات
chmod -R 775 storage bootstrap/cache
```

3. **مشاكل الـ Cache:**
```bash
# مسح جميع أنواع الـ cache
php artisan optimize:clear
```

## 📝 السجلات

يتم حفظ السجلات في:
- `storage/logs/laravel.log` - سجلات التطبيق
- `storage/logs/firebase.log` - سجلات Firebase

## 🤝 المساهمة

1. Fork المشروع
2. إنشاء فرع للميزة (`git checkout -b feature/AmazingFeature`)
3. Commit التغييرات (`git commit -m 'Add some AmazingFeature'`)
4. Push للفرع (`git push origin feature/AmazingFeature`)
5. فتح Pull Request

## 📄 الترخيص

هذا المشروع مرخص تحت رخصة MIT - راجع ملف [LICENSE](LICENSE) للتفاصيل.

## 👨‍💻 المطور

- **اسمك** - [GitHub](https://github.com/your-username)
- **البريد الإلكتروني** - <EMAIL>

## 🙏 شكر وتقدير

- [Laravel](https://laravel.com) - إطار العمل
- [Firebase](https://firebase.google.com) - الإشعارات الفورية
- [Tailwind CSS](https://tailwindcss.com) - التصميم
- [Alpine.js](https://alpinejs.dev) - التفاعل

---

⭐ إذا أعجبك هذا المشروع، لا تنس إعطاؤه نجمة!
