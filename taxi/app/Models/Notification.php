<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Auth;


class Notification extends Model
{
    protected $fillable = ['user_id','sender_id','title_ar', 'body_ar',  'title_en', 'body_en', 'data', 'read'];

    public function user()
    {
        return $this->belongsTo(User::class, 'user_id');
    }

     public function sender()
    {
        return $this->belongsTo(User::class, 'sender_id');
    }

    public static function countUnreadMessages($userId)
    {
        return self::where('user_id', $userId)
            ->where('read', false)
            ->count();
    }
}
