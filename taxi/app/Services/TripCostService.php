<?php

namespace App\Services;

class TripCostService
{
    protected float $startFare;
    protected float $dayKmRate;
    protected float $nightKmRate;

    public function __construct(float $startFare, float $dayKmRate, float $nightKmRate)
    {
        $this->startFare = $startFare;
        $this->dayKmRate = $dayKmRate;
        $this->nightKmRate = $nightKmRate;
    }

    public function calculate(float $distance, ?\DateTime $tripTime = null): float
    {
        $tripTime = $tripTime ?? new \DateTime();
        $hour = (int) $tripTime->format('H');
        $isNight = ($hour >= 20 || $hour < 6);
        $ratePerKm = $isNight ? $this->nightKmRate : $this->dayKmRate;
        $total = $this->startFare + ($distance * $ratePerKm);
        return round($total, 2);
    }
}
