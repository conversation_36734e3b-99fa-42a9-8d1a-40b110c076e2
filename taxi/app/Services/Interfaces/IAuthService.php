<?php

namespace App\Services\Interfaces;

use App\Models\User;
use App\Models\OTPVerification;

interface IAuthService
{
    public function register(array $data): User;
    public function login(string $phone, string $password, string $role): User;
    public function verifyOTP(User $user, string $otpCode): bool;
    public function forgetPassword(string $phone): bool;
    public function resetPassword(User $user, string $password);
    public function incrementOtpAttempts(User $user);
    public function getOtpRecord(User $user): ?OTPVerification;
}
