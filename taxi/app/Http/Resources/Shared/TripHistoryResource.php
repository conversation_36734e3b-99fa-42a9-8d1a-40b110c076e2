<?php

namespace App\Http\Resources\Shared;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class TripHistoryResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'trip' => [
                'id' => $this->trip->id ?? null,
                'pickup_location' => $this->trip->pickup_location ?? null,
                'dropoff_location' => $this->trip->dropoff_location ?? null,
                'distance' => $this->trip->distance ?? null,
                'fare' => $this->trip->fare ?? null,
                'status' => $this->trip->status ?? null,
                'driver_id' => $this->trip->driver_id ?? null,
            ],
            'user' => [
                'id' => $this->user->id ?? null,
                'name' => $this->user->name ?? null,
                'email' => $this->user->email ?? null,
                'avatar' =>  $this->user->avatar ? env('APP_URL').$this->user->avatar : null,

            ],
            'created_at' => $this->created_at,
        ];
    }
}
