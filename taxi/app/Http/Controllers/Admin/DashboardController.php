<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Notifications\SystemWideNotification;
use App\Models\EmailNotfiction;
use App\Models\EmailUserNotfiction;
use App\Jobs\SendBatchEmailsJob;
use App\Services\AudiLogsService;

use App\Models\User;
use App\Models\Trip;
use App\Models\WalletTransaction;
use App\Models\Wallet;

class DashboardController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $totalUsers = User::count();
        $totalDrivers = User::role('driver')->count();
        $totalTrips = Trip::count();
        $totalEarnings = WalletTransaction::where('reference_type', 'ride_commission')->sum('amount');
        $completedTrips = Trip::where('status', 'completed')->count();
        $cancelledTrips = Trip::where('status', 'cancelled')->count();
        $ongoingTrips = Trip::whereNotIn('status', ['completed', 'cancelled'])->count();
        return view('admin.dashboard', [
            'totalUsers' => $totalUsers,
            'totalDrivers' => $totalDrivers,
            'totalTrips' => $totalTrips,
            'totalEarnings' => $totalEarnings,
            'completedTrips' => $completedTrips,
            'cancelledTrips' => $cancelledTrips,
            'ongoingTrips' => $ongoingTrips,
        ]);
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        //
    }
}
