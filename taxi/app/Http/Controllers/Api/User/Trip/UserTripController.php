<?php

namespace App\Http\Controllers\Api\User\Trip;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Services\Location\GeohashService;
use App\Services\Location\DistanceService;
use Illuminate\Support\Facades\Auth;
use App\Models\DriverAvailability;
use App\Models\Trip;
use App\Models\Vehicle\VehicleType;
use Carbon\Carbon;
use GPBMetadata\Google\Api\Log;
use Kreait\Firebase\Factory;
use Kreait\Firebase\Messaging\CloudMessage;
use Illuminate\Support\Facades\Validator;
use App\Services\Firebase\FirebaseService;
use App\Models\Vehicle\Vehicle;
use App\Models\DriverProfile;

class UserTripController extends Controller
{
    protected $geohashService;
    protected $distanceService;
    protected $firebase;

    public function __construct(GeohashService $geohashService, DistanceService $distanceService, FirebaseService $firebaseService)
    {
        $this->geohashService = $geohashService;
        $this->distanceService = $distanceService;
        $this->firebase = $firebaseService;

    }


    public function fetchVehicleTypes(Request $request)
    {

        $validator = Validator::make($request->all(), [
            'start_latitude' => 'required|numeric|between:-90,90',
            'start_longitude' => 'required|numeric|between:-180,180',
            'end_latitude' => 'required|numeric|between:-90,90',
            'end_longitude' => 'required|numeric|between:-180,180',
            'promo_code' => 'nullable|string|in:DISCOUNT10,DISCOUNT20',
            'ETA' => 'nullable|integer|min:1',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }
        $startLat = $request->start_latitude;
        $startLong = $request->start_longitude;
        $endLat = $request->end_latitude;
        $endLong = $request->end_longitude;

        // Validate that all necessary location data is provided
        if (!$startLat || !$startLong || !$endLat || !$endLong) {
            return response()->json([
                'message' => 'Start and end locations (latitude and longitude) are required.',
            ], 400);
        }

        $distance = $this->calculateDistance($startLat, $startLong, $endLat, $endLong);

        $vehicleTypes = VehicleType::where('is_active', true)
            ->get([
                'id', 'name', 'icon_url', 'start_fare',
                'day_per_km_rate', 'night_per_km_rate',
                'day_per_minute_rate', 'night_per_minute_rate'
            ])
            ->map(function ($type) use ($request, $distance) {

                if ($type->icon_url) {
                    $type->icon_url = asset('storage/' . $type->icon_url);
                }

                $currentHour = Carbon::now()->hour;
                $isNightTime = ($currentHour >= 18 || $currentHour < 6);

                $perKmRate = $isNightTime ? $type->night_per_km_rate : $type->day_per_km_rate;
                $perMinuteRate = $isNightTime ? $type->night_per_minute_rate : $type->day_per_minute_rate;

                $promoCodeDiscount = 0;
                if ($request->promo_code && $request->promo_code === "DISCOUNT10") {
                    $promoCodeDiscount = 10;
                }

                $ETA = $request->ETA ?? 10; // Default to 10 minutes if no ETA is provided

                $totalBeforeDiscount = $type->start_fare + ($distance * $perKmRate) + ($ETA * $perMinuteRate);
                $total = $totalBeforeDiscount - ($totalBeforeDiscount * $promoCodeDiscount) / 100;

                return [
                    'name' => $type->name,
                    'avatar' => $type->icon_url,
                    'price' => $total,
                ];
            });

        return response()->json([
            'status' => true,
            'vehicle_types' => $vehicleTypes,
        ]);
    }


    public function requestTrip(Request $request)
    {

        $validator = Validator::make($request->all(), [
            'start_latitude' => 'required|numeric|between:-90,90',
            'start_longitude' => 'required|numeric|between:-180,180',
            'end_latitude' => 'required|numeric|between:-90,90',
            'end_longitude' => 'required|numeric|between:-180,180',
            'pickup_name' => 'required|string|max:255',  // Validate pickup name
            'dropoff_name' => 'required|string|max:255', // Validate dropoff name
            'vehicle_type_id' => 'required|exists:vehicle_types,id',
            'estimated_fare' => 'nullable|numeric|min:0', // Validate estimated fare if provided

        ]);

        if ($validator->fails()) {
            return response()->json([
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        $userLat = $request->start_latitude;
        $userLong = $request->start_longitude;
        $endLat = $request->end_latitude;
        $endLong = $request->end_longitude;
        $pickupName = $request->pickup_name;
        $dropoffName = $request->dropoff_name;
        $vehicleTypeId = $request->vehicle_type_id;

        if (!$userLat || !$userLong || !$endLat || !$endLong) {
            return response()->json([
                'message' => 'Start and end locations (latitude and longitude) are required.',
            ], 400);
        }

        $this->cancelPendingTrip();

        // Step 1: Calculate the geohash for the user's location
        try {
            $userGeohash = $this->geohashService->encode($userLat, $userLong);
            $drivers = $this->getNearbyDrivers($userGeohash, $vehicleTypeId);

            if (empty($drivers)) {
                return response()->json(['message' => 'No drivers available with the selected vehicle type.'], 404);
            }

            // ... rest of the code
        } catch (\Exception $e) {
            \Log::error("Trip request failed: " . $e->getMessage());
            return response()->json(['message' => 'Internal server error'], 500);
        }

        // Step 3: Send notifications to available drivers
        $tripId = $this->createTrip($userLat, $userLong, $endLat, $endLong, $vehicleTypeId); // You can generate a trip ID or retrieve one from DB

        $this->firebase->storeTripInFirebase($tripId, 'pending');

        foreach ($drivers as $driver) {
            $this->sendDriverNotification($driver, $tripId, $pickupName, $dropoffName);
        }

        return response()->json([
            'message' => 'Trip request sent to nearby drivers.',
            'status' => true,
        ]);
    }

     public function cancelTrip(Request $request, $tripId)
    {
        try {
            $trip = Trip::findOrFail($tripId);

            // Ensure the authenticated user is the one requesting the cancellation
            if ($trip->user_id !== auth()->user()->id) {
                return response()->json(['message' => 'Unauthorized, not the user who created the trip.'], 403);
            }

            // Only allow cancellation if the trip is in 'accepted' state (before starting)
            if ($trip->status !== 'accepted' && $trip->status !== 'pending') {
                return response()->json(['message' => 'Trip cannot be cancelled at this stage.'], 400);
            }

            // Update the status to 'cancelled'
            $trip->status = 'cancelled';
            $trip->cancelled_by = 'user'; // Indicating that the user cancelled the trip
            $trip->cancelled_at = now();
            $trip->save();

            // Update the status in Firebase
            $this->firebase->storeTripInFirebase($trip->id, 'cancelled');

            return response()->json([
                'message' => 'Trip cancelled successfully.',
                'trip' => $trip
            ]);
        } catch (\Exception $e) {
            return response()->json(['message' => 'Error cancelling the trip: ' . $e->getMessage()], 500);
        }
    }

private function getNearbyDrivers($userGeohash, $vehicleTypeId)
{
  try {

        $drivers = $this->firebase->getDriversByGeohashPrefix($userGeohash);

        if (empty($drivers)) {
            \Log::info("No drivers found for geohash: " . $userGeohash);
            return [];
        }

        $availableDrivers = [];
        if ($drivers) {
            foreach ($drivers as $driverId => $driverData) {

                if (isset(
                    $driverData['geohash'],
                    $driverData['lat'],
                    $driverData['long'])) {

                        $driverProfile = DriverProfile::where('user_id', $driverId)->first();



                        $vehicle = Vehicle::where('driver_profile_id', $driverProfile->id)
                                  ->first();


                                \Log::info("Checking driver: " . $driverId . " for vehicle type: " . $vehicleTypeId);

                                if ($driverProfile->is_driver_verified && $driverProfile->driver->is_online && $this->isDriverAvailable($driverId) && $vehicle->vehicle_type_id == $vehicleTypeId) {
                        $availableDrivers[] = [
                            'driver_id' => $driverId,
                            'geohash' => $driverData['geohash'],
                            'lat' => $driverData['lat'],
                            'long' => $driverData['long']
                        ];
                    }
                }
            }
        }
        \Log::info("Found " . count($availableDrivers) . " available drivers for geohash: " . $userGeohash);

        return $availableDrivers;

    } catch (\Exception $e) {
        \Log::error("Firebase error: " . $e->getMessage());
        return [];
    }
}

    private function isDriverAvailable($driverId)
    {
        $driverAvailability = DriverAvailability::where('driver_id', $driverId)->first();
        if (!$driverAvailability) {
            \Log::info("No availability record found for driver: " . $driverId);
            return true;
        }
        return $driverAvailability->is_available;
    }

    private function createTrip($userLat, $userLong, $endLat, $endLong, $vehicleTypeId)
    {
        $pickupName = request()->pickup_name;
        $dropoffName = request()->dropoff_name;
        $estimatedFare = request()->estimated_fare;


        // Ensure the correct user is creating the trip
        $userId = auth()->id(); // Get the authenticated user's ID

        // Debugging: Log the values being saved
        \Log::info("Creating trip with user_id: $userId, pickup_name: $pickupName, dropoff_name: $dropoffName");

        $trip = new Trip();
        $trip->user_id = $userId; // Set user_id from authenticated user
        $trip->pickup_lat = $userLat;
        $trip->pickup_lng = $userLong;
        $trip->dropoff_lat = $endLat;
        $trip->dropoff_lng = $endLong;
        $trip->pickup_name = $pickupName;
        $trip->dropoff_name = $dropoffName;
        $trip->estimated_fare = $estimatedFare; // Set estimated fare if provided
        $trip->vehicle_type_id = $vehicleTypeId; // Save the vehicle type ID
        $trip->status = 'pending';
        $trip->save();

        \Log::info("Trip created successfully: " . json_encode($trip));


        return $trip->id;
    }

    private function sendDriverNotification($driver, $tripId, $pickupName, $dropoffName)
    {
        try {

            // Prepare notification data
            $message = CloudMessage::new()
                ->withTarget('token', $driver['firebase_token'])
                ->withData([
                    'trip_id' => $tripId,
                    'user_lat' => $driver['lat'],
                    'user_long' => $driver['long'],
                    'pickup_name' => $pickupName,
                    'dropoff_name' => $dropoffName,
                    'start_time' => now()->toDateTimeString(),
                ]
            );
        } catch (\Exception $e) {
            \Log::error("Error sending Firebase notification to driver: " . $e->getMessage());
        }
    }



    protected function calculateGeohashDistance($userGeoHash, $driverGeoHash)
    {
        $user = $this->geohashService->decode($userGeoHash);
        $driver = $this->geohashService->decode($driverGeoHash);

        return $this->distanceService->haversine(
            $user['latitude'], $user['longitude'],
            $driver['latitude'], $driver['longitude']
        );
    }


    protected function calculateDistance($startLat, $startLong, $endLat, $endLong)
    {
        $earthRadius = 6371;

        $latDiff = deg2rad($endLat - $startLat);
        $longDiff = deg2rad($endLong - $startLong);

        $a = sin($latDiff / 2) * sin($latDiff / 2) +
             cos(deg2rad($startLat)) * cos(deg2rad($endLat)) *
             sin($longDiff / 2) * sin($longDiff / 2);

        $c = 2 * atan2(sqrt($a), sqrt(1 - $a));

        // Distance in kilometers
        return $earthRadius * $c;
    }


    private function cancelPendingTrip()
{
    $userId = auth()->id();

    $pendingTrip = Trip::where('user_id', $userId)
                        ->where('status', 'pending')
                        ->orderBy('id', 'desc')
                        ->first();

    if ($pendingTrip) {
        $pendingTrip->status = 'cancelled';
        $pendingTrip->cancelled_by = 'user';
        $pendingTrip->cancelled_at = now();
        $pendingTrip->save();

        \Log::info("Cancelled the previous pending trip with ID: " . $pendingTrip->id);
    }
}

    public function getUserTrip($tripId)
    {
        try {
            // Find the trip by ID and ensure the trip belongs to the authenticated user
            $trip = Trip::where('user_id', auth()->id()) // Ensure the user is the one who created the trip
                        ->findOrFail($tripId);

            return response()->json([
                'message' => 'Trip fetched successfully.',
                'trip' => $trip
            ]);
        } catch (\Exception $e) {
            return response()->json(['message' => 'Error fetching the trip: ' . $e->getMessage()], 500);
        }
    }
}
