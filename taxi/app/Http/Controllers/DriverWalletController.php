<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\User;
use App\Models\Wallet;
use App\Models\WalletTransaction;
use App\Models\DriverProfile;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;
use Barryvdh\DomPDF\Facade\Pdf;
use Yajra\DataTables\DataTables;

class DriverWalletController extends Controller
{
    public function index()
    {
        return view('admin.wallet.drivers' );
    }
    public function getDriverWallets(Request $request)
    {
        $wallets = Wallet::with(['user'])
            ->whereHas('user.roles', function ($q) {
                $q->where('name', 'driver');
            });
        return DataTables::of($wallets)
            ->addColumn('driver_name', function ($wallet) {
                return $wallet->user ? $wallet->user->name : null;
            })
            ->addColumn('email', function ($wallet) {
                return $wallet->user ? $wallet->user->email : null;
            })
            ->addColumn('phone', function ($wallet) {
                return $wallet->user ? $wallet->user->phone : null;
            })
            ->addColumn('last_transaction', function ($wallet) {
                return $wallet->transactions()->max('created_at');
            })
            // ->addColumn('action', function ($wallet) {
            //     $url = route('admin.profile.wallets.show', ['user' => $wallet->user_id]);
            //     return '<a href="' . $url . '" class="btn btn-sm btn-primary">show</a>';
            // })
            // ->rawColumns(['last_transaction', 'action'])

            ->rawColumns(['last_transaction'])
            ->make(true);
    }
    public function exportDriverWallets()
    {
        $wallets = Wallet::with(['user'])
            ->whereHas('user.roles', function ($query) {
                $query->where('name', 'driver');
            })
            ->orderBy('id', 'desc')
            ->get();
        $headers = [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => 'attachment; filename="driver_wallets.csv"',
        ];
        $callback = function () use ($wallets) {
            $file = fopen('php://output', 'w');
            fputcsv($file, ['ID', 'User ID', 'Name', 'Email', 'Phone', 'Balance', 'Created At']);
            foreach ($wallets as $wallet) {
                $user = $wallet->user;
                fputcsv($file, [
                    $wallet->id,
                    $wallet->user_id,
                    $user ? $user->name : null,
                    $user ? $user->email : null,
                    $user ? $user->phone : null,
                    $wallet->balance,
                    $wallet->created_at
                ]);
            }
            fclose($file);
        };
        return response()->stream($callback, 200, $headers);
    }
    public function exportDriverWalletsPdf()
    {
        $wallets = Wallet::with(['user'])
            ->whereHas('user.roles', function ($query) {
                $query->where('name', 'driver');
            })
            ->orderBy('id', 'desc')
            ->get();
        $pdf = Pdf::loadView('admin.wallet.drivers_pdf', compact('wallets'));
        return $pdf->download('driver_wallets.pdf');
    }
}
