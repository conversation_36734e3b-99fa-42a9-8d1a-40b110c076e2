<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;

class FirebaseController extends Controller
{
    /**
     * Get Firebase configuration for client-side use
     * 
     * @return JsonResponse
     */
    public function getConfig(): JsonResponse
    {
        $config = config('firebase-client.config');
        
        // Remove any sensitive data if needed
        // Only return what's needed for client-side Firebase initialization
        $clientConfig = [
            'apiKey' => $config['apiKey'],
            'authDomain' => $config['authDomain'],
            'databaseURL' => $config['databaseURL'],
            'projectId' => $config['projectId'],
            'storageBucket' => $config['storageBucket'],
            'messagingSenderId' => $config['messagingSenderId'],
            'appId' => $config['appId'],
        ];

        // Add measurementId if it exists
        if (!empty($config['measurementId'])) {
            $clientConfig['measurementId'] = $config['measurementId'];
        }

        return response()->json([
            'config' => $clientConfig,
            'vapidKey' => config('firebase-client.vapidKey')
        ]);
    }
}
