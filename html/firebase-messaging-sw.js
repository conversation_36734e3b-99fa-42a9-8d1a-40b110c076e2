importScripts("https://www.gstatic.com/firebasejs/8.10.1/firebase-app.js");
importScripts("https://www.gstatic.com/firebasejs/8.10.1/firebase-messaging.js");

// Firebase configuration - replace with your actual config
const firebaseConfig = {
    apiKey: "AIzaSyAcdXn5P712MrHTfkH2Eewz8LAl0Twlt20",
    authDomain: "taxi-c0045.firebaseapp.com",
    projectId: "taxi-c0045",
    storageBucket: "taxi-c0045.appspot.com",
    messagingSenderId: "754590061481",
    appId: "1:754590061481:web:c21166049ca20c22a82ab8"
};

// Initialize Firebase
try {
    firebase.initializeApp(firebaseConfig);
    const messaging = firebase.messaging();

    console.log('✅ Firebase initialized in service worker');

    // Set up background message handler
    messaging.onBackgroundMessage(function (payload) {
        console.log("[firebase-messaging-sw.js] Received background message", payload);

        // Get notification language from localStorage (fallback to 'en')
        const notificationLang = 'ar'; // Default to Arabic

        // Extract notification data
        const title = (payload.data && payload.data[`title_${notificationLang}`])
            ? payload.data[`title_${notificationLang}`]
            : (payload.notification ? payload.notification.title : 'New Notification');

        const body = (payload.data && payload.data[`body_${notificationLang}`])
            ? payload.data[`body_${notificationLang}`]
            : (payload.notification ? payload.notification.body : '');

        const notificationOptions = {
            body: body,
            icon: "/assets/images/favicon-32x32.png",
            badge: "/assets/images/favicon-32x32.png",
            tag: payload.data?.notification_id || 'default',
            requireInteraction: true,
            actions: [
                {
                    action: 'view',
                    title: 'View',
                    icon: '/assets/images/favicon-32x32.png'
                }
            ]
        };

        self.registration.showNotification(title, notificationOptions);
    });

} catch (error) {
    console.error('❌ Failed to initialize Firebase in service worker:', error);
}
